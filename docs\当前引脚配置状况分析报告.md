# TI_CAR1.6 当前引脚配置状况分析报告

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-30
- **分析人员**: Emma (产品经理)
- **分析目标**: 深入分析编码器和PWM引脚配置，确认与用户要求的差异

## 分析概述

本报告深入分析TI_CAR1.6工程中编码器和PWM引脚的当前配置状况，通过对比用户要求的引脚配置，确认了具体的不匹配项和影响范围。

## 1. 当前引脚配置详细分析

### 1.1 编码器引脚配置（SPD_READER）

**配置文件定义（ti_msp_dl_config.h）：**

```c
// SPD_READER_A 组（编码器A相）
#define SPD_READER_A_PORT                    (GPIOB)
#define SPD_READER_A_FONT_LEFT_A_PIN         (DL_GPIO_PIN_11)  // PB11
#define SPD_READER_A_FONT_RIGHT_A_PIN        (DL_GPIO_PIN_9)   // PB9

// SPD_READER_B 组（编码器B相）
#define SPD_READER_B_PORT                    (GPIOB)
#define SPD_READER_B_FONT_LEFT_B_PIN         (DL_GPIO_PIN_10)  // PB10
#define SPD_READER_B_FONT_RIGHT_B_PIN        (DL_GPIO_PIN_8)   // PB8
```

**当前映射关系：**
- 左电机编码器A: PB11 (SPD_READER_A_FONT_LEFT_A_PIN)
- 左电机编码器B: PB10 (SPD_READER_B_FONT_LEFT_B_PIN)
- 右电机编码器A: PB9 (SPD_READER_A_FONT_RIGHT_A_PIN)
- 右电机编码器B: PB8 (SPD_READER_B_FONT_RIGHT_B_PIN)

### 1.2 PWM控制引脚配置

**MotorAFront（左电机）配置：**
```c
#define MotorAFront_INST                     TIMG7
#define GPIO_MotorAFront_C0_PIN              DL_GPIO_PIN_15    // PB15
#define GPIO_MotorAFront_C1_PIN              DL_GPIO_PIN_16    // PB16
```

**MotorBFront（右电机）配置：**
```c
#define MotorBFront_INST                     TIMA1
#define GPIO_MotorBFront_C0_PIN              DL_GPIO_PIN_2     // PB2
#define GPIO_MotorBFront_C1_PIN              DL_GPIO_PIN_3     // PB3
```

**当前PWM映射关系：**
- 左电机PWM: PB15 (C0), PB16 (C1) - MotorAFront (TIMG7)
- 右电机PWM: PB2 (C0), PB3 (C1) - MotorBFront (TIMA1)

## 2. 用户要求的引脚配置

**用户要求：**
- **右电机**：编码器A(PB11)、编码器B(PB10)、PWM(PB16、PB15)
- **左电机**：编码器A(PB9)、编码器B(PB8)、PWM(PB2、PB3)

## 3. 问题识别与差异分析

### 3.1 ❌ 编码器引脚配置问题

**核心问题：左右电机编码器引脚配置完全颠倒**

| 电机 | 当前配置 | 用户要求 | 状态 |
|------|----------|----------|------|
| 左电机编码器A | PB11 | PB9 | ❌ 不匹配 |
| 左电机编码器B | PB10 | PB8 | ❌ 不匹配 |
| 右电机编码器A | PB9 | PB11 | ❌ 不匹配 |
| 右电机编码器B | PB8 | PB10 | ❌ 不匹配 |

**问题详细说明：**
- 当前配置中，LEFT标识的引脚实际对应用户要求的右电机引脚
- 当前配置中，RIGHT标识的引脚实际对应用户要求的左电机引脚
- 这是一个完全的左右映射颠倒问题

### 3.2 ✅ PWM引脚配置正确

| 电机 | 当前配置 | 用户要求 | 状态 |
|------|----------|----------|------|
| 左电机PWM | PB15, PB16 | PB2, PB3 | ❌ 不匹配 |
| 右电机PWM | PB2, PB3 | PB16, PB15 | ❌ 不匹配 |

**重要发现：PWM引脚也存在左右颠倒问题**
- 当前左电机使用PB15/PB16，但用户要求左电机应使用PB2/PB3
- 当前右电机使用PB2/PB3，但用户要求右电机应使用PB15/PB16

## 4. 数据流分析

### 4.1 编码器数据流

**Motor实例配置（BSP/Src/Motor.c）：**
```c
// 左前轮 - 使用Data_MotorEncoder[0]
MOTOR_Def_t Motor_Font_Left = {
    .Motor_Encoder_Addr = &Data_MotorEncoder[0],
    .Motor_PWM_TIMX = MotorAFront_INST,  // TIMG7
    // ...
};

// 右前轮 - 使用Data_MotorEncoder[1]  
MOTOR_Def_t Motor_Font_Right = {
    .Motor_Encoder_Addr = &Data_MotorEncoder[1],
    .Motor_PWM_TIMX = MotorBFront_INST,  // TIMA1
    // ...
};
```

**中断处理逻辑（APP/Src/Interrupt.c）：**
```c
if (ISR_IS_GPIO(SPD_READER_A_FONT_LEFT_A_PIN)) //左前轮
{
    // 更新Data_MotorEncoder[0]
    if (GET_RDR_B_VAL(SPD_READER_B_FONT_LEFT_B_PIN)) 
        (*Motor_Font_Left.Motor_Encoder_Addr)++;
    else 
        (*Motor_Font_Left.Motor_Encoder_Addr)--;
}

if (ISR_IS_GPIO(SPD_READER_A_FONT_RIGHT_A_PIN)) //右前轮
{
    // 更新Data_MotorEncoder[1]
    if (GET_RDR_B_VAL(SPD_READER_B_FONT_RIGHT_B_PIN)) 
        (*Motor_Font_Right.Motor_Encoder_Addr)++;
    else 
        (*Motor_Font_Right.Motor_Encoder_Addr)--;
}
```

### 4.2 数据流一致性

**当前数据流：**
- PB11中断 → 更新Data_MotorEncoder[0] (左电机)
- PB9中断 → 更新Data_MotorEncoder[1] (右电机)

**用户要求的数据流：**
- PB9中断 → 应更新Data_MotorEncoder[0] (左电机)
- PB11中断 → 应更新Data_MotorEncoder[1] (右电机)

## 5. 影响范围评估

### 5.1 功能影响
- **编码器反馈系统**：左右电机的编码器数据会被错误地分配
- **PID控制系统**：左右电机的速度反馈数据颠倒，影响控制精度
- **运动控制**：车辆的转向和直行控制可能出现异常

### 5.2 系统稳定性
- 不影响系统编译和基本运行
- 不影响PWM输出功能
- 不影响其他外设功能

## 6. 修正策略建议

### 6.1 应用层重映射方案（推荐）
- 保持底层配置文件（ti_msp_dl_config.h）不变
- 在应用层通过宏定义重新映射LEFT和RIGHT
- 修改中断处理逻辑使用新的映射关系
- 保持Data_MotorEncoder数组索引不变

### 6.2 优势分析
- 符合"不改动底层和框架层"的要求
- 保持现有架构和数据流不变
- 修改范围小，风险可控
- 便于后续维护和理解

## 7. 结论

通过深入分析，确认TI_CAR1.6工程中存在编码器和PWM引脚的左右映射颠倒问题。当前配置与用户要求完全相反，需要通过应用层重映射来解决。PWM引脚配置同样存在左右颠倒问题，需要一并修正。

建议采用应用层宏定义重映射的方案，既能解决引脚配置问题，又能保持系统架构的稳定性。
