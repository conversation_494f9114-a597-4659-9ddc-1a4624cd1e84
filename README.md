## Example Summary

TI_CAR1.6 嵌入式系统项目 - 基于MSPM0G3507微控制器的双电机控制系统。
本项目使用DriverLib库实现DRV8871双PWM电机驱动控制，支持编码器反馈和PID控制。

### 🔧 引脚配置修正说明 (2025-07-30)

**重要更新**: 本项目已完成电机引脚配置修正，解决了编码器和PWM引脚左右映射颠倒的问题。

- **修正方式**: 应用层重映射机制 (不修改底层框架)
- **详细文档**: 请参阅 `docs/TI_CAR1.6_引脚配置修正报告.md`
- **引脚对照**: 请参阅 `docs/引脚对应关系表.md`

## Peripherals & Pin Assignments

### 系统外设
| Peripheral | Pin | Function |
| --- | --- | --- |
| SYSCTL |  |  |
| DEBUGSS | PA20 | Debug Clock |
| DEBUGSS | PA19 | Debug Data In Out |

### 电机控制引脚 (修正后配置)
| 电机 | 功能 | 引脚 | 定时器 | 说明 |
| --- | --- | --- | --- | --- |
| **左电机** | 编码器A相 | **PB9** | - | 逻辑左电机编码器A |
| **左电机** | 编码器B相 | **PB8** | - | 逻辑左电机编码器B |
| **左电机** | PWM IN1 | **PB2** | TIMA1 CC0 | DRV8871 IN1控制 |
| **左电机** | PWM IN2 | **PB3** | TIMA1 CC1 | DRV8871 IN2控制 |
| **右电机** | 编码器A相 | **PB11** | - | 逻辑右电机编码器A |
| **右电机** | 编码器B相 | **PB10** | - | 逻辑右电机编码器B |
| **右电机** | PWM IN1 | **PB16** | TIMG7 CC0 | DRV8871 IN1控制 |
| **右电机** | PWM IN2 | **PB15** | TIMG7 CC1 | DRV8871 IN2控制 |

> **注意**: 上述引脚配置通过应用层重映射实现，与用户硬件连接要求完全一致。

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## Example Usage

### 编译和运行
1. 使用TI Code Composer Studio打开项目
2. 编译项目 (确保无错误和警告)
3. 下载到LP_MSPM0G3507开发板
4. 连接DRV8871电机驱动器和编码器按照引脚配置表

### 硬件连接
请按照上述**电机控制引脚**表进行硬件连接，确保：
- 左电机编码器连接到PB9(A相)和PB8(B相)
- 左电机DRV8871的IN1/IN2连接到PB2/PB3
- 右电机编码器连接到PB11(A相)和PB10(B相)
- 右电机DRV8871的IN1/IN2连接到PB16/PB15

### 重要文档
- **引脚配置修正报告**: `docs/TI_CAR1.6_引脚配置修正报告.md`
- **引脚对应关系表**: `docs/引脚对应关系表.md`

### 技术支持
如遇到引脚配置相关问题，请参考上述文档或检查应用层重映射配置文件 `APP/Inc/MotorPinRemap.h`。
