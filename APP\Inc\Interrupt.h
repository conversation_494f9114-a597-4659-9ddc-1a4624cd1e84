#ifndef __Interrupt_h
#define __Interrupt_h

#include "SysConfig.h"
#include "MotorPinRemap.h"

// 编码器中断处理统一宏定义 - 使用重映射后的逻辑引脚
#define SPD_READER_CLR_ISR_FLAG(X) DL_GPIO_clearInterruptStatus(SPD_READER_A_PORT, (X))
#define SPD_READER_GET_ISR_STATUS(X) DL_GPIO_getEnabledInterruptStatus(SPD_READER_A_PORT, (X))

extern uint8_t enable_group1_irq;
extern bool Flag_MPU6050_Ready; //MPU6050是否准备好

void Interrupt_Init(void);

#endif
