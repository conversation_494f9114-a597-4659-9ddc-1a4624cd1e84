#ifndef __Motor_h
#define __Motor_h

#include "SysConfig.h"

/**
 * @brief 电机方向
 * 
 */
typedef enum
{
    DIRC_NONE = 0,
    DIRC_FOWARD, //正转
    DIRC_BACKWARD //反转
} Motor_DIRC_Def_t;

/**
 * @brief 电机控制结构体 - DRV8871双PWM控制模式
 *
 */
typedef struct
{
    GPTIMER_Regs *Motor_PWM_TIMX; //PWM对应的定时器
    __IO int Motor_PWM_CH1; //PWM通道1 (IN1 - DRV8871)
    __IO int Motor_PWM_CH2; //PWM通道2 (IN2 - DRV8871)
    uint32_t Motor_PWM_Period; //PWM周期值
    bool Is_TimerG; //定时器类型标志 (true=TimerG, false=TimerA)
    int16_t *Motor_Encoder_Addr; //电机编码值地址
    Motor_DIRC_Def_t Motor_Dirc; //电机当前正转还是反转
    PID_Def_t Motor_PID_Instance; //PID实例
} MOTOR_Def_t;

extern MOTOR_Def_t Motor_Font_Left; //左前轮
extern MOTOR_Def_t Motor_Font_Right; //左后轮

void Motor_Start(void);
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value);
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time);

#endif