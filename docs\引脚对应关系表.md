# TI_CAR1.6 引脚对应关系表

## 文档信息
- **更新日期**: 2025-07-30
- **版本**: v1.0
- **状态**: 引脚配置修正后

## 1. 电机引脚对应关系总览

### 1.1 用户硬件连接要求 vs 修正后配置

| 电机位置 | 功能 | 用户要求引脚 | 修正后逻辑配置 | 物理映射 | 定时器 |
|---------|------|-------------|---------------|----------|--------|
| **左电机** | 编码器A相 | **PB9** | LOGICAL_LEFT_ENCODER_A_PIN | SPD_READER_A_FONT_RIGHT_A_PIN | - |
| **左电机** | 编码器B相 | **PB8** | LOGICAL_LEFT_ENCODER_B_PIN | SPD_READER_B_FONT_RIGHT_B_PIN | - |
| **左电机** | PWM IN1 | **PB2** | LOGICAL_LEFT_PWM_TIMER CH1 | MotorBFront_INST CC0 | TIMA1 |
| **左电机** | PWM IN2 | **PB3** | LOGICAL_LEFT_PWM_TIMER CH2 | MotorBFront_INST CC1 | TIMA1 |
| **右电机** | 编码器A相 | **PB11** | LOGICAL_RIGHT_ENCODER_A_PIN | SPD_READER_A_FONT_LEFT_A_PIN | - |
| **右电机** | 编码器B相 | **PB10** | LOGICAL_RIGHT_ENCODER_B_PIN | SPD_READER_B_FONT_LEFT_B_PIN | - |
| **右电机** | PWM IN1 | **PB16** | LOGICAL_RIGHT_PWM_TIMER CH1 | MotorAFront_INST CC0 | TIMG7 |
| **右电机** | PWM IN2 | **PB15** | LOGICAL_RIGHT_PWM_TIMER CH2 | MotorAFront_INST CC1 | TIMG7 |

## 2. 详细引脚映射表

### 2.1 编码器引脚映射

#### 左电机编码器 (用户要求: PB9/PB8)
```
逻辑配置                     物理映射                        实际引脚
LOGICAL_LEFT_ENCODER_A_PIN → SPD_READER_A_FONT_RIGHT_A_PIN → PB9
LOGICAL_LEFT_ENCODER_B_PIN → SPD_READER_B_FONT_RIGHT_B_PIN → PB8
```

#### 右电机编码器 (用户要求: PB11/PB10)
```
逻辑配置                      物理映射                       实际引脚
LOGICAL_RIGHT_ENCODER_A_PIN → SPD_READER_A_FONT_LEFT_A_PIN → PB11
LOGICAL_RIGHT_ENCODER_B_PIN → SPD_READER_B_FONT_LEFT_B_PIN → PB10
```

### 2.2 PWM定时器映射

#### 左电机PWM (用户要求: PB2/PB3)
```
逻辑配置                  物理映射              实际引脚    定时器类型
LOGICAL_LEFT_PWM_TIMER → MotorBFront_INST → PB2/PB3 → TIMA1
- PWM_CH1 (IN1)       → CC0_INDEX        → PB2
- PWM_CH2 (IN2)       → CC1_INDEX        → PB3
- PWM_PERIOD          → 1000             → TimerA周期值
- IS_TIMERG           → false            → TimerA类型
```

#### 右电机PWM (用户要求: PB16/PB15)
```
逻辑配置                   物理映射              实际引脚      定时器类型
LOGICAL_RIGHT_PWM_TIMER → MotorAFront_INST → PB16/PB15 → TIMG7
- PWM_CH1 (IN1)        → CC0_INDEX        → PB16
- PWM_CH2 (IN2)        → CC1_INDEX        → PB15  
- PWM_PERIOD           → 100              → TimerG周期值
- IS_TIMERG            → true             → TimerG类型
```

## 3. 编码器数据数组映射

### 3.1 数据数组索引对应关系

| 逻辑电机 | 编码器数组索引 | 数组变量 | 说明 |
|---------|---------------|----------|------|
| 左电机 | LOGICAL_LEFT_ENCODER_INDEX (1) | Data_MotorEncoder[1] | 逻辑左电机使用索引1 |
| 右电机 | LOGICAL_RIGHT_ENCODER_INDEX (0) | Data_MotorEncoder[0] | 逻辑右电机使用索引0 |

### 3.2 电机实例配置映射

| 电机实例 | 编码器地址 | PWM定时器 | 说明 |
|---------|-----------|-----------|------|
| Motor_Font_Left | &Data_MotorEncoder[1] | LOGICAL_LEFT_PWM_TIMER | 逻辑左电机实例 |
| Motor_Font_Right | &Data_MotorEncoder[0] | LOGICAL_RIGHT_PWM_TIMER | 逻辑右电机实例 |

## 4. 中断处理映射

### 4.1 GPIO中断引脚映射

| 中断处理 | 修正前引脚 | 修正后逻辑引脚 | 实际物理引脚 |
|---------|-----------|---------------|-------------|
| 左前轮中断 | SPD_READER_A_FONT_LEFT_A_PIN (PB11) | LOGICAL_LEFT_ENCODER_A_PIN | PB9 |
| 右前轮中断 | SPD_READER_A_FONT_RIGHT_A_PIN (PB9) | LOGICAL_RIGHT_ENCODER_A_PIN | PB11 |

### 4.2 中断处理函数映射

```c
// GROUP1_IRQHandler 中的引脚检查
修正前: ISR_IS_GPIO(SPD_READER_A_FONT_LEFT_A_PIN)   // PB11
修正后: ISR_IS_GPIO(LOGICAL_LEFT_ENCODER_A_PIN)     // PB9

修正前: ISR_IS_GPIO(SPD_READER_A_FONT_RIGHT_A_PIN)  // PB9  
修正后: ISR_IS_GPIO(LOGICAL_RIGHT_ENCODER_A_PIN)    // PB11
```

## 5. DRV8871 电机驱动器连接

### 5.1 左电机连接 (用户硬件)
```
DRV8871 左电机驱动器:
- IN1 ← PB2 (TIMA1 CC0) ← LOGICAL_LEFT_PWM_TIMER CH1
- IN2 ← PB3 (TIMA1 CC1) ← LOGICAL_LEFT_PWM_TIMER CH2
- 编码器A ← PB9 ← LOGICAL_LEFT_ENCODER_A_PIN
- 编码器B ← PB8 ← LOGICAL_LEFT_ENCODER_B_PIN
```

### 5.2 右电机连接 (用户硬件)
```
DRV8871 右电机驱动器:
- IN1 ← PB16 (TIMG7 CC0) ← LOGICAL_RIGHT_PWM_TIMER CH1  
- IN2 ← PB15 (TIMG7 CC1) ← LOGICAL_RIGHT_PWM_TIMER CH2
- 编码器A ← PB11 ← LOGICAL_RIGHT_ENCODER_A_PIN
- 编码器B ← PB10 ← LOGICAL_RIGHT_ENCODER_B_PIN
```

## 6. 重映射配置验证

### 6.1 编译时验证宏

| 验证项 | 验证宏 | 期望值 | 说明 |
|-------|--------|--------|------|
| 左电机编码器A | LOGICAL_LEFT_ENCODER_A_PIN | DL_GPIO_PIN_9 | 必须为PB9 |
| 左电机编码器B | LOGICAL_LEFT_ENCODER_B_PIN | DL_GPIO_PIN_8 | 必须为PB8 |
| 右电机编码器A | LOGICAL_RIGHT_ENCODER_A_PIN | DL_GPIO_PIN_11 | 必须为PB11 |
| 右电机编码器B | LOGICAL_RIGHT_ENCODER_B_PIN | DL_GPIO_PIN_10 | 必须为PB10 |

### 6.2 配置正确性检查清单

- [ ] 左电机编码器引脚: PB9(A), PB8(B) ✅
- [ ] 左电机PWM引脚: PB2(IN1), PB3(IN2) ✅  
- [ ] 右电机编码器引脚: PB11(A), PB10(B) ✅
- [ ] 右电机PWM引脚: PB16(IN1), PB15(IN2) ✅
- [ ] 定时器类型匹配: TIMA1(左), TIMG7(右) ✅
- [ ] 编码器数组索引: 左电机[1], 右电机[0] ✅

## 7. 故障排查参考

### 7.1 常见问题及解决方案

| 问题现象 | 可能原因 | 检查项目 | 解决方案 |
|---------|---------|----------|----------|
| 左右电机方向相反 | 引脚映射错误 | 检查LOGICAL_*_ENCODER_*_PIN定义 | 确认重映射配置正确 |
| 编码器计数异常 | 中断处理映射错误 | 检查GROUP1_IRQHandler中的引脚使用 | 确认使用LOGICAL_*引脚定义 |
| PWM输出异常 | 定时器映射错误 | 检查LOGICAL_*_PWM_TIMER定义 | 确认定时器实例正确 |
| 编译错误 | 头文件包含问题 | 检查MotorPinRemap.h包含顺序 | 确保在ti_msp_dl_config.h之后包含 |

### 7.2 调试验证方法

1. **编译时验证**: 利用#error宏进行编译时检查
2. **运行时验证**: 使用MOTOR_REMAP_DEBUG_INFO()宏输出配置信息
3. **硬件验证**: 使用示波器检查PWM输出和编码器信号
4. **功能验证**: 运行电机控制程序验证方向和速度控制

---
**文档结束**

> **注意**: 本文档记录了TI_CAR1.6项目引脚配置修正后的完整映射关系。如有任何配置变更，请及时更新本文档。
