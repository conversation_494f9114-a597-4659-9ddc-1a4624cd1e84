/**
 * @file Interrupt.c
 * <AUTHOR> name (<EMAIL>)
 * @brief 存储的是各种中断相关的
 * @version 0.1
 * @date 2025-07-12
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "Interrupt.h"

uint8_t enable_group1_irq = 0;
bool Flag_Serial_RXcplt = false; //接收完成标志位
uint32_t ExISR_Flag; //中断判断标志位
bool Flag_MPU6050_Ready = false; //MPU6050是否准备好

#define ISR_IS_GPIO(X)        (ExISR_Flag & (X))
#define GET_RDR_B_VAL(X)      DL_GPIO_readPins(SPD_READER_B_PORT, (X))

/**
 * @brief Systick时钟中断
 * 
 */
void SysTick_Handler(void)
{
    SysTick_Increasment();
}

/**
 * @brief 串口0中断
 * 
 */
void UART_0_INST_IRQHandler(void)
{
    //DMA接收完成中断
    if (DL_UART_Main_getPendingInterrupt(UART_WIT_INST) == DL_UART_MAIN_IIDX_DMA_DONE_RX)
    {
        if (!DL_UART_isRXFIFOEmpty(UART_WIT_INST))
        {
        }
        DL_UART_clearInterruptStatus(UART_WIT_INST, DL_UART_MAIN_IIDX_DMA_DONE_RX);
    }
    //发送完成中断
    if (DL_UART_Main_getPendingInterrupt(UART_WIT_INST) == DL_UART_MAIN_IIDX_EOT_DONE)
    {
        DL_DMA_disableChannel(DMA, DMA_CH_TX_CHAN_ID);
        LED_BOARD_TOGGLE();
        DL_UART_clearInterruptStatus(UART_WIT_INST, DL_UART_MAIN_IIDX_EOT_DONE);
    }
}

/**
 * @brief 外部中断
 * 
 */
void GROUP1_IRQHandler(void)
{
    //判断是不是由GPIOA触发的中断
    if (DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1) == SPD_READER_A_INT_IIDX)
    {
        //查看哪个IO进入外部中断 - 使用重映射后的逻辑引脚
        ExISR_Flag = SPD_READER_GET_ISR_STATUS(LOGICAL_LEFT_ENCODER_A_PIN | LOGICAL_RIGHT_ENCODER_A_PIN);

        if (ISR_IS_GPIO(LOGICAL_LEFT_ENCODER_A_PIN)) //逻辑左前轮
        {
            if (GET_RDR_B_VAL(LOGICAL_LEFT_ENCODER_B_PIN)) (*Motor_Font_Left.Motor_Encoder_Addr)++;
            else (*Motor_Font_Left.Motor_Encoder_Addr)--;
            SPD_READER_CLR_ISR_FLAG(LOGICAL_LEFT_ENCODER_A_PIN);
        }

        if (ISR_IS_GPIO(LOGICAL_RIGHT_ENCODER_A_PIN)) //逻辑右前轮
        {
            if (GET_RDR_B_VAL(LOGICAL_RIGHT_ENCODER_B_PIN)) (*Motor_Font_Right.Motor_Encoder_Addr)++;
            else (*Motor_Font_Right.Motor_Encoder_Addr)--;
            SPD_READER_CLR_ISR_FLAG(LOGICAL_RIGHT_ENCODER_A_PIN);
        }
    }
}

void UART_WIT_INST_IRQHandler(void)
{
    uint8_t checkSum, packCnt = 0;
    extern uint8_t wit_dmaBuffer[33];

    DL_DMA_disableChannel(DMA, DMA_WIT_CHAN_ID);
    uint8_t rxSize = 32 - DL_DMA_getTransferSize(DMA, DMA_WIT_CHAN_ID);

    if(DL_UART_isRXFIFOEmpty(UART_WIT_INST) == false)
        wit_dmaBuffer[rxSize++] = DL_UART_receiveData(UART_WIT_INST);

    while(rxSize >= 11)
    {
        checkSum=0;
        for(int i=packCnt*11; i<(packCnt+1)*11-1; i++)
            checkSum += wit_dmaBuffer[i];

        if((wit_dmaBuffer[packCnt*11] == 0x55) && (checkSum == wit_dmaBuffer[packCnt*11+10]))
        {
            if(wit_dmaBuffer[packCnt*11+1] == 0x51)
            {
                wit_data.ax = (int16_t)((wit_dmaBuffer[packCnt*11+3]<<8)|wit_dmaBuffer[packCnt*11+2]) / 2.048; //mg
                wit_data.ay = (int16_t)((wit_dmaBuffer[packCnt*11+5]<<8)|wit_dmaBuffer[packCnt*11+4]) / 2.048; //mg
                wit_data.az = (int16_t)((wit_dmaBuffer[packCnt*11+7]<<8)|wit_dmaBuffer[packCnt*11+6]) / 2.048; //mg
                wit_data.temperature =  (int16_t)((wit_dmaBuffer[packCnt*11+9]<<8)|wit_dmaBuffer[packCnt*11+8]) / 100.0; //°C
            }
            else if(wit_dmaBuffer[packCnt*11+1] == 0x52)
            {
                wit_data.gx = (int16_t)((wit_dmaBuffer[packCnt*11+3]<<8)|wit_dmaBuffer[packCnt*11+2]) / 16.384; //°/S
                wit_data.gy = (int16_t)((wit_dmaBuffer[packCnt*11+5]<<8)|wit_dmaBuffer[packCnt*11+4]) / 16.384; //°/S
                wit_data.gz = (int16_t)((wit_dmaBuffer[packCnt*11+7]<<8)|wit_dmaBuffer[packCnt*11+6]) / 16.384; //°/S
            }
            else if(wit_dmaBuffer[packCnt*11+1] == 0x53)
            {
                wit_data.roll  = (int16_t)((wit_dmaBuffer[packCnt*11+3]<<8)|wit_dmaBuffer[packCnt*11+2]) / 32768.0 * 180.0; //°
                wit_data.pitch = (int16_t)((wit_dmaBuffer[packCnt*11+5]<<8)|wit_dmaBuffer[packCnt*11+4]) / 32768.0 * 180.0; //°
                wit_data.yaw   = (int16_t)((wit_dmaBuffer[packCnt*11+7]<<8)|wit_dmaBuffer[packCnt*11+6]) / 32768.0 * 180.0; //°
                wit_data.version = (int16_t)((wit_dmaBuffer[packCnt*11+9]<<8)|wit_dmaBuffer[packCnt*11+8]);
            }
        }

        rxSize -= 11;
        packCnt++;
    }
    
    uint8_t dummy[4];
    DL_UART_drainRXFIFO(UART_WIT_INST, dummy, 4);

    DL_DMA_setDestAddr(DMA, DMA_WIT_CHAN_ID, (uint32_t) &wit_dmaBuffer[0]);
    DL_DMA_setTransferSize(DMA, DMA_WIT_CHAN_ID, 32);
    DL_DMA_enableChannel(DMA, DMA_WIT_CHAN_ID);
}



/**
 * @brief 中断初始化
 * 
 */
void Interrupt_Init(void)
{
    NVIC_EnableIRQ(1);

    DL_UART_clearInterruptStatus(UART_WIT_INST, DL_UART_MAIN_IIDX_EOT_DONE);
    SPD_READER_CLR_ISR_FLAG(LOGICAL_LEFT_ENCODER_A_PIN);
    SPD_READER_CLR_ISR_FLAG(LOGICAL_RIGHT_ENCODER_A_PIN);
}


