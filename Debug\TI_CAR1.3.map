******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 29 22:14:23 2025

OUTPUT FILE NAME:   <TI_CAR1.3.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004ea1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000064f0  00019b10  R  X
  SRAM                  20200000   00008000  00000bc2  0000743e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000064f0   000064f0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005930   00005930    r-x .text
  000059f0    000059f0    00000aa0   00000aa0    r-- .rodata
  00006490    00006490    00000060   00000060    r-- .cinit
20200000    20200000    000009c2   00000000    rw-
  20200000    20200000    000004f0   00000000    rw- .bss
  202004f0    202004f0    00000400   00000000    rw- .sysmem
  202008f0    202008f0    000000d2   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005930     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000368     Interrupt.o (.text.UART0_IRQHandler)
                  00000df8    000002e0     Task_App.o (.text.Task_AutoRecover)
                  000010d8    00000254     wit.o (.text.wit_direct)
                  0000132c    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000154c    0000020c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001758    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001934    000001b0     Task.o (.text.Task_Start)
                  00001ae4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001c76    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001c78    0000015c     Tracker.o (.text.Tracker_Read)
                  00001dd4    00000144     PID.o (.text.PID_SProsc)
                  00001f18    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002054    00000134            : qsort.c.obj (.text.qsort)
                  00002188    00000130     OLED.o (.text.OLED_ShowChar)
                  000022b8    0000012c     PID.o (.text.PID_AProsc)
                  000023e4    00000120     Task_App.o (.text.Task_Motor_PID)
                  00002504    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002624    00000110     OLED.o (.text.OLED_Init)
                  00002734    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00002840    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  0000294c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002a50    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002b34    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002c10    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002ce8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002dc0    000000d4     Motor.o (.text.Motor_SetDuty)
                  00002e94    000000d0     Task_App.o (.text.Task_Init)
                  00002f64    000000c4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003028    000000b4     Task.o (.text.Task_Add)
                  000030dc    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  0000318c    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000322e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003230    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000032c8    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00003358    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  000033e4    0000008c     Task_App.o (.text.Task_wit)
                  00003470    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000034fc    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003580    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_K230_init)
                  00003600    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000367c    00000074     Motor.o (.text.Motor_Start)
                  000036f0    00000074     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  00003764    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000037d8    00000008     Interrupt.o (.text.SysTick_Handler)
                  000037e0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003854    00000074     SysTick.o (.text.delay_us)
                  000038c8    0000006e     OLED.o (.text.OLED_ShowString)
                  00003936    00000002     --HOLE-- [fill = 0]
                  00003938    0000006c     Task_App.o (.text.Task_OLED)
                  000039a4    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00003a0e    00000002     --HOLE-- [fill = 0]
                  00003a10    00000068     Task_App.o (.text.Task_Key)
                  00003a78    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003ae0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003b46    00000002     --HOLE-- [fill = 0]
                  00003b48    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00003bac    00000064     Key_Led.o (.text.Key_Read)
                  00003c10    00000064     wit.o (.text.WIT_Init)
                  00003c74    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003cd6    00000002     --HOLE-- [fill = 0]
                  00003cd8    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003d3a    00000002     --HOLE-- [fill = 0]
                  00003d3c    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003d9c    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003dfc    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003e5a    00000002     --HOLE-- [fill = 0]
                  00003e5c    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003eb8    0000005c     Task_App.o (.text.Task_Tracker)
                  00003f14    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003f70    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00003fcc    00000058     Serial.o (.text.Serial_Init)
                  00004024    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000407c    00000058            : _printfi.c.obj (.text._pconv_f)
                  000040d4    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000412a    00000002     --HOLE-- [fill = 0]
                  0000412c    00000054     Motor.o (.text.CalculateDutyValue)
                  00004180    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  000041d4    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004228    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000427a    00000002     --HOLE-- [fill = 0]
                  0000427c    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000042cc    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000431c    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004368    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000043b4    0000004c     OLED.o (.text.OLED_Printf)
                  00004400    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000444a    00000002     --HOLE-- [fill = 0]
                  0000444c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004494    00000048     Motor.o (.text.Motor_GetSpeed)
                  000044dc    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00004524    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004568    00000044     Motor.o (.text.SetPWMValue)
                  000045ac    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000045ee    00000002     --HOLE-- [fill = 0]
                  000045f0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_bujingA_init)
                  00004630    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_bujingB_init)
                  00004670    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000046b0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000046f0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004730    0000003e     Task.o (.text.Task_CMP)
                  0000476e    00000002     --HOLE-- [fill = 0]
                  00004770    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000047ac    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000047e8    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004824    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00004860    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  0000489c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000048d8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004914    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000494e    00000002     --HOLE-- [fill = 0]
                  00004950    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000498a    00000002     --HOLE-- [fill = 0]
                  0000498c    00000038     Interrupt.o (.text.Interrupt_Init)
                  000049c4    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000049fc    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004a30    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004a64    00000034     Task_App.o (.text.Task_IdleFunction)
                  00004a98    00000030     Interrupt.o (.text.DL_DMA_setTransferSize)
                  00004ac8    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00004af8    00000030     wit.o (.text.DL_DMA_setTransferSize)
                  00004b28    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00004b58    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00004b88    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004bb8    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00004be4    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00004c10    0000002c     wit.o (.text.__NVIC_EnableIRQ)
                  00004c3c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004c68    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00004c94    0000002a     PID.o (.text.PID_Init)
                  00004cbe    00000028     OLED.o (.text.DL_Common_updateReg)
                  00004ce6    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004d0e    00000002     --HOLE-- [fill = 0]
                  00004d10    00000028     Interrupt.o (.text.DL_DMA_setDestAddr)
                  00004d38    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00004d60    00000028     wit.o (.text.DL_DMA_setDestAddr)
                  00004d88    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00004db0    00000028     wit.o (.text.DL_DMA_setSrcAddr)
                  00004dd8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004e00    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004e28    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004e50    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00004e78    00000028     SysTick.o (.text.SysTick_Increasment)
                  00004ea0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004ec8    00000026     Interrupt.o (.text.DL_DMA_disableChannel)
                  00004eee    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00004f14    00000026     Interrupt.o (.text.DL_DMA_enableChannel)
                  00004f3a    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00004f60    00000026     wit.o (.text.DL_DMA_enableChannel)
                  00004f86    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004fac    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004fd2    00000002     --HOLE-- [fill = 0]
                  00004fd4    00000024     Interrupt.o (.text.DL_DMA_getTransferSize)
                  00004ff8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  0000501c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00005040    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00005064    00000022     PID.o (.text.PID_SetParams)
                  00005086    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000050a8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000050c8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000050e8    00000020     SysTick.o (.text.Delay)
                  00005108    00000020     main.o (.text.main)
                  00005128    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00005146    00000002     --HOLE-- [fill = 0]
                  00005148    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00005166    00000002     --HOLE-- [fill = 0]
                  00005168    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00005184    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000051a0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000051bc    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  000051d8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000051f4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00005210    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0000522c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00005248    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00005264    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00005280    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  0000529c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000052b8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000052d4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000052f0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000530c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00005328    0000001c     wit.o (.text.WIT_SetTargetAngle)
                  00005344    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  0000535c    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00005374    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000538c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000053a4    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000053bc    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000053d4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000053ec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00005404    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000541c    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00005434    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  0000544c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005464    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  0000547c    00000018     Task_App.o (.text.DL_GPIO_togglePins)
                  00005494    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000054ac    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000054c4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000054dc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000054f4    00000018     OLED.o (.text.DL_I2C_enablePower)
                  0000550c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00005524    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  0000553c    00000018     OLED.o (.text.DL_I2C_reset)
                  00005554    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  0000556c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005584    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  0000559c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000055b4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000055cc    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000055e4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000055fc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00005614    00000018     Motor.o (.text.DL_Timer_startCounter)
                  0000562c    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00005644    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  0000565c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00005674    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  0000568c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000056a4    00000018     Interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  000056bc    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000056d4    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000056ec    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  00005704    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  0000571c    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00005732    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00005748    00000016     OLED.o (.text.DL_GPIO_readPins)
                  0000575e    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00005774    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000578a    00000016     wit.o (.text.__f64_bits_as_u64)
                  000057a0    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000057b4    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  000057c8    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000057dc    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000057f0    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005804    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00005818    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000582c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005840    00000014     Interrupt.o (.text.DL_UART_receiveData)
                  00005854    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005868    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000587c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000588e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000058a0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000058b2    00000002     --HOLE-- [fill = 0]
                  000058b4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000058c4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000058d4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000058e4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000058f4    00000010     wit.o (.text.WIT_IsTargetControlActive)
                  00005904    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005914    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00005924    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005932    0000000e     wit.o (.text.__f32_bits_as_u32)
                  00005940    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000594e    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000595c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00005968    0000000c     wit.o (.text.WIT_CancelTargetControl)
                  00005974    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00005980    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000598a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005994    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000059a4    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000059ae    0000000a            : vsprintf.c.obj (.text._outc)
                  000059b8    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000059c0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000059c8    00000006     libc.a : exit.c.obj (.text:abort)
                  000059ce    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000059d2    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000059d6    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000059da    00000002     --HOLE-- [fill = 0]
                  000059dc    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000059ec    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00006490    00000060     
                  00006490    0000003a     (.cinit..data.load) [load image, compression = lzss]
                  000064ca    00000002     --HOLE-- [fill = 0]
                  000064cc    0000000c     (__TI_handler_table)
                  000064d8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000064e0    00000010     (__TI_cinit_table)

.rodata    0    000059f0    00000aa0     
                  000059f0    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00005fe0    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00006208    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00006210    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006311    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00006352    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00006354    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000637c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00006394    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  000063ac    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  000063c4    00000013     Task_App.o (.rodata.str1.5883415095785080416.1)
                  000063d7    00000012     Task_App.o (.rodata.str1.11952760121962574671.1)
                  000063e9    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000063fa    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  0000640b    00000010     Task_App.o (.rodata.str1.14074990341397557290.1)
                  0000641b    0000000c     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00006427    0000000b     Task_App.o (.rodata.str1.492715258893803702.1)
                  00006432    0000000a     ti_msp_dl_config.o (.rodata.gUART_K230Config)
                  0000643c    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  00006446    0000000a     ti_msp_dl_config.o (.rodata.gUART_bujingAConfig)
                  00006450    0000000a     ti_msp_dl_config.o (.rodata.gUART_bujingBConfig)
                  0000645a    00000002     ti_msp_dl_config.o (.rodata.gUART_K230ClockConfig)
                  0000645c    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00006464    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000646c    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00006472    00000005     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00006477    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000647b    00000004     Task_App.o (.rodata.str1.8896853068034818020.1)
                  0000647f    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00006482    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00006485    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00006487    00000002     ti_msp_dl_config.o (.rodata.gUART_bujingAClockConfig)
                  00006489    00000002     ti_msp_dl_config.o (.rodata.gUART_bujingBClockConfig)
                  0000648b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000004f0     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorBFrontBackup)
                  202003ac    000000a0     (.common:gMotorAFrontBackup)
                  2020044c    00000030     (.common:gUART_bujingBBackup)
                  2020047c    0000002c     wit.o (.bss.Angle_PID_Instance)
                  202004a8    00000021     (.common:wit_dmaBuffer)
                  202004c9    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  202004ca    00000001     (.common:ret)
                  202004cb    00000001     --HOLE--
                  202004cc    00000020     (.common:wit_data)
                  202004ec    00000004     (.common:ExISR_Flag)

.sysmem    0    202004f0    00000400     UNINITIALIZED
                  202004f0    00000010     libc.a : memory.c.obj (.sysmem)
                  20200500    000003f0     --HOLE--

.data      0    202008f0    000000d2     UNINITIALIZED
                  202008f0    00000048     Motor.o (.data.Motor_Font_Left)
                  20200938    00000048     Motor.o (.data.Motor_Font_Right)
                  20200980    00000008     Task_App.o (.data.Data_Tracker_Input)
                  20200988    00000008     Task_App.o (.data.Motor)
                  20200990    00000004     Task_App.o (.data.Data_MotorEncoder)
                  20200994    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  20200998    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  2020099c    00000004     Task_App.o (.data.Data_wit_Offset)
                  202009a0    00000004     Task_App.o (.data.Data_wit_Target)
                  202009a4    00000004     Task_App.o (.data.Data_wit_UserTarget)
                  202009a8    00000004     Task_App.o (.data.Task_AutoRecover.line_found_time)
                  202009ac    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202009b0    00000004     SysTick.o (.data.delayTick)
                  202009b4    00000004     Task_App.o (.data.lost_time)
                  202009b8    00000004     SysTick.o (.data.uwTick)
                  202009bc    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202009be    00000001     Task_App.o (.data.Data_wit_ControlEnabled)
                  202009bf    00000001     Task.o (.data.Task_Num)
                  202009c0    00000001     Task_App.o (.data.current_path)
                  202009c1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3598    184       396    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3638    376       396    
                                                               
    .\APP\Src\
       Task_App.o                     1752    103       53     
       Interrupt.o                    1526    0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         3278    103       58     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       wit.o                          998     0         109    
       Task.o                         674     0         241    
       Serial.o                       292     0         512    
       Motor.o                        576     0         144    
       PID.o                          700     0         0      
       Tracker.o                      414     0         1      
       SysTick.o                      200     0         8      
       Key_Led.o                      122     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5834    2072      1015   
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1136    0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5730    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2814    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       94        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   22794   3001      3009   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000064e0 records: 2, size/record: 8, table size: 16
	.data: load addr=00006490, load size=0000003a bytes, run addr=202008f0, run size=000000d2 bytes, compression=lzss
	.bss: load addr=000064d8, load size=00000008 bytes, run addr=20200000, run size=000004f0 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000064cc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001ae5     00005994     00005992   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004ea1     000059dc     000059d6   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000059cf  ADC0_IRQHandler                      
000059cf  ADC1_IRQHandler                      
000059cf  AES_IRQHandler                       
000059d2  C$$EXIT                              
000059cf  CANFD0_IRQHandler                    
000059cf  DAC0_IRQHandler                      
00005981  DL_Common_delayCycles                
0000431d  DL_DMA_initChannel                   
00003dfd  DL_I2C_fillControllerTXFIFO          
00004fad  DL_I2C_setClockConfig                
00002b35  DL_SYSCTL_configSYSPLL               
00003b49  DL_SYSCTL_setHFCLKSourceHFXTParams   
00004525  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000294d  DL_Timer_initFourCCPWMMode           
000052d5  DL_Timer_setCaptCompUpdateMethod     
000055fd  DL_Timer_setCaptureCompareOutCtl     
000058c5  DL_Timer_setCaptureCompareValue      
000052f1  DL_Timer_setClockConfig              
00004181  DL_UART_drainRXFIFO                  
0000444d  DL_UART_init                         
0000587d  DL_UART_setClockConfig               
000059cf  DMA_IRQHandler                       
20200990  Data_MotorEncoder                    
20200994  Data_Motor_TarSpeed                  
20200980  Data_Tracker_Input                   
20200998  Data_Tracker_Offset                  
202009be  Data_wit_ControlEnabled              
2020099c  Data_wit_Offset                      
202009a0  Data_wit_Target                      
202009a4  Data_wit_UserTarget                  
000059cf  Default_Handler                      
000050e9  Delay                                
202004ec  ExISR_Flag                           
000059cf  GROUP0_IRQHandler                    
000030dd  GROUP1_IRQHandler                    
000059d3  HOSTexit                             
000059cf  HardFault_Handler                    
000059cf  I2C0_IRQHandler                      
000059cf  I2C1_IRQHandler                      
000039a5  I2C_OLED_Clear                       
00004825  I2C_OLED_Set_Pos                     
00003231  I2C_OLED_WR_Byte                     
00003d3d  I2C_OLED_i2c_sda_unlock              
0000498d  Interrupt_Init                       
00003bad  Key_Read                             
20200988  Motor                                
202008f0  Motor_Font_Left                      
20200938  Motor_Font_Right                     
00004495  Motor_GetSpeed                       
00002dc1  Motor_SetDuty                        
0000367d  Motor_Start                          
000059cf  NMI_Handler                          
00002625  OLED_Init                            
000043b5  OLED_Printf                          
00002189  OLED_ShowChar                        
000038c9  OLED_ShowString                      
000022b9  PID_AProsc                           
00004c95  PID_Init                             
00001dd5  PID_SProsc                           
00005065  PID_SetParams                        
000059cf  PendSV_Handler                       
000059cf  RTC_IRQHandler                       
000059d7  Reset_Handler                        
000059cf  SPI0_IRQHandler                      
000059cf  SPI1_IRQHandler                      
000059cf  SVC_Handler                          
00004b29  SYSCFG_DL_DMA_CH_RX_init             
000056d5  SYSCFG_DL_DMA_CH_TX_init             
000056ed  SYSCFG_DL_DMA_WIT_init               
000058d5  SYSCFG_DL_DMA_init                   
0000154d  SYSCFG_DL_GPIO_init                  
00003d9d  SYSCFG_DL_I2C_OLED_init              
00003359  SYSCFG_DL_MotorAFront_init           
000032c9  SYSCFG_DL_MotorBFront_init           
00003e5d  SYSCFG_DL_SYSCTL_init                
000058e5  SYSCFG_DL_SYSTICK_init               
00003581  SYSCFG_DL_UART_K230_init             
000036f1  SYSCFG_DL_UART_WIT_init              
000045f1  SYSCFG_DL_UART_bujingA_init          
00004631  SYSCFG_DL_UART_bujingB_init          
000041d5  SYSCFG_DL_init                       
00002f65  SYSCFG_DL_initPower                  
00003fcd  Serial_Init                          
20200000  Serial_RxData                        
000037d9  SysTick_Handler                      
00004e79  SysTick_Increasment                  
0000595d  Sys_GetTick                          
000059cf  TIMA0_IRQHandler                     
000059cf  TIMA1_IRQHandler                     
000059cf  TIMG0_IRQHandler                     
000059cf  TIMG12_IRQHandler                    
000059cf  TIMG6_IRQHandler                     
000059cf  TIMG7_IRQHandler                     
000059cf  TIMG8_IRQHandler                     
0000588f  TI_memcpy_small                      
0000594f  TI_memset_small                      
00003029  Task_Add                             
00000df9  Task_AutoRecover                     
00004a65  Task_IdleFunction                    
00002e95  Task_Init                            
00003a11  Task_Key                             
000023e5  Task_Motor_PID                       
00003939  Task_OLED                            
00001935  Task_Start                           
00003eb9  Task_Tracker                         
000033e5  Task_wit                             
00001c79  Tracker_Read                         
00000a91  UART0_IRQHandler                     
000059cf  UART1_IRQHandler                     
000059cf  UART2_IRQHandler                     
000059cf  UART3_IRQHandler                     
00005969  WIT_CancelTargetControl              
00003c11  WIT_Init                             
000058f5  WIT_IsTargetControlActive            
00005329  WIT_SetTargetAngle                   
00002735  _IQ24div                             
00004bb9  _IQ24mpy                             
00004b59  _IQ24toF                             
00006311  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000064e0  __TI_CINIT_Base                      
000064f0  __TI_CINIT_Limit                     
000064f0  __TI_CINIT_Warm                      
000064cc  __TI_Handler_Table_Base              
000064d8  __TI_Handler_Table_Limit             
000048d9  __TI_auto_init_nobinit_nopinit       
00003601  __TI_decompress_lzss                 
000058a1  __TI_decompress_none                 
00004025  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005915  __TI_zero_init                       
00001aef  __adddf3                             
00002cf3  __addsf3                             
00006210  __aeabi_ctype_table_                 
00006210  __aeabi_ctype_table_C                
000037e1  __aeabi_d2f                          
00004401  __aeabi_d2iz                         
000045ad  __aeabi_d2uiz                        
00001aef  __aeabi_dadd                         
00003c75  __aeabi_dcmpeq                       
00003cb1  __aeabi_dcmpge                       
00003cc5  __aeabi_dcmpgt                       
00003c9d  __aeabi_dcmple                       
00003c89  __aeabi_dcmplt                       
00002841  __aeabi_ddiv                         
00002a51  __aeabi_dmul                         
00001ae5  __aeabi_dsub                         
202009ac  __aeabi_errno                        
000059b9  __aeabi_errno_addr                   
000046b1  __aeabi_f2d                          
000049c5  __aeabi_f2iz                         
00002cf3  __aeabi_fadd                         
00003cd9  __aeabi_fcmpeq                       
00003d15  __aeabi_fcmpge                       
00003d29  __aeabi_fcmpgt                       
00003d01  __aeabi_fcmple                       
00003ced  __aeabi_fcmplt                       
00003471  __aeabi_fmul                         
00002ce9  __aeabi_fsub                         
00004c3d  __aeabi_i2d                          
00004861  __aeabi_i2f                          
000040d5  __aeabi_idiv                         
00001c77  __aeabi_idiv0                        
000040d5  __aeabi_idivmod                      
0000322f  __aeabi_ldiv0                        
00005149  __aeabi_llsl                         
00005041  __aeabi_lmul                         
00005975  __aeabi_memclr                       
00005975  __aeabi_memclr4                      
00005975  __aeabi_memclr8                      
000059c1  __aeabi_memcpy                       
000059c1  __aeabi_memcpy4                      
000059c1  __aeabi_memcpy8                      
00005925  __aeabi_memset                       
00005925  __aeabi_memset4                      
00005925  __aeabi_memset8                      
0000501d  __aeabi_ui2d                         
00004671  __aeabi_uidiv                        
00004671  __aeabi_uidivmod                     
00005855  __aeabi_uldivmod                     
00005149  __ashldi3                            
ffffffff  __binit__                            
00003a79  __cmpdf2                             
00004915  __cmpsf2                             
00002841  __divdf3                             
00003a79  __eqdf2                              
00004915  __eqsf2                              
000046b1  __extendsfdf2                        
00004401  __fixdfsi                            
000049c5  __fixsfsi                            
000045ad  __fixunsdfsi                         
00004c3d  __floatsidf                          
00004861  __floatsisf                          
0000501d  __floatunsidf                        
00003765  __gedf2                              
0000489d  __gesf2                              
00003765  __gtdf2                              
0000489d  __gtsf2                              
00003a79  __ledf2                              
00004915  __lesf2                              
00003a79  __ltdf2                              
00004915  __ltsf2                              
UNDEFED   __mpu_init                           
00002a51  __muldf3                             
00005041  __muldi3                             
00004951  __muldsi3                            
00003471  __mulsf3                             
00003a79  __nedf2                              
00004915  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001ae5  __subdf3                             
00002ce9  __subsf3                             
000037e1  __truncdfsf2                         
0000318d  __udivmoddi4                         
00004ea1  _c_int00_noargs                      
202004f0  _sys_memory                          
UNDEFED   _system_post_cinit                   
000059ed  _system_pre_init                     
000059c9  abort                                
00005fe0  asc2_0806                            
000059f0  asc2_1608                            
000046f1  atoi                                 
ffffffff  binit                                
202009b0  delayTick                            
00003855  delay_us                             
202009c1  enable_group1_irq                    
00003f15  frexp                                
00003f15  frexpl                               
202003ac  gMotorAFrontBackup                   
202002f0  gMotorBFrontBackup                   
2020044c  gUART_bujingBBackup                  
00000000  interruptVectors                     
00002c11  ldexp                                
00002c11  ldexpl                               
00005109  main                                 
00005087  memccpy                              
00002055  qsort                                
202004ca  ret                                  
00002c11  scalbn                               
00002c11  scalbnl                              
202009b8  uwTick                               
00004c69  vsprintf                             
00005905  wcslen                               
202004cc  wit_data                             
000010d9  wit_direct                           
202004a8  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000a91  UART0_IRQHandler                     
00000df9  Task_AutoRecover                     
000010d9  wit_direct                           
0000154d  SYSCFG_DL_GPIO_init                  
00001935  Task_Start                           
00001ae5  __aeabi_dsub                         
00001ae5  __subdf3                             
00001aef  __adddf3                             
00001aef  __aeabi_dadd                         
00001c77  __aeabi_idiv0                        
00001c79  Tracker_Read                         
00001dd5  PID_SProsc                           
00002055  qsort                                
00002189  OLED_ShowChar                        
000022b9  PID_AProsc                           
000023e5  Task_Motor_PID                       
00002625  OLED_Init                            
00002735  _IQ24div                             
00002841  __aeabi_ddiv                         
00002841  __divdf3                             
0000294d  DL_Timer_initFourCCPWMMode           
00002a51  __aeabi_dmul                         
00002a51  __muldf3                             
00002b35  DL_SYSCTL_configSYSPLL               
00002c11  ldexp                                
00002c11  ldexpl                               
00002c11  scalbn                               
00002c11  scalbnl                              
00002ce9  __aeabi_fsub                         
00002ce9  __subsf3                             
00002cf3  __addsf3                             
00002cf3  __aeabi_fadd                         
00002dc1  Motor_SetDuty                        
00002e95  Task_Init                            
00002f65  SYSCFG_DL_initPower                  
00003029  Task_Add                             
000030dd  GROUP1_IRQHandler                    
0000318d  __udivmoddi4                         
0000322f  __aeabi_ldiv0                        
00003231  I2C_OLED_WR_Byte                     
000032c9  SYSCFG_DL_MotorBFront_init           
00003359  SYSCFG_DL_MotorAFront_init           
000033e5  Task_wit                             
00003471  __aeabi_fmul                         
00003471  __mulsf3                             
00003581  SYSCFG_DL_UART_K230_init             
00003601  __TI_decompress_lzss                 
0000367d  Motor_Start                          
000036f1  SYSCFG_DL_UART_WIT_init              
00003765  __gedf2                              
00003765  __gtdf2                              
000037d9  SysTick_Handler                      
000037e1  __aeabi_d2f                          
000037e1  __truncdfsf2                         
00003855  delay_us                             
000038c9  OLED_ShowString                      
00003939  Task_OLED                            
000039a5  I2C_OLED_Clear                       
00003a11  Task_Key                             
00003a79  __cmpdf2                             
00003a79  __eqdf2                              
00003a79  __ledf2                              
00003a79  __ltdf2                              
00003a79  __nedf2                              
00003b49  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003bad  Key_Read                             
00003c11  WIT_Init                             
00003c75  __aeabi_dcmpeq                       
00003c89  __aeabi_dcmplt                       
00003c9d  __aeabi_dcmple                       
00003cb1  __aeabi_dcmpge                       
00003cc5  __aeabi_dcmpgt                       
00003cd9  __aeabi_fcmpeq                       
00003ced  __aeabi_fcmplt                       
00003d01  __aeabi_fcmple                       
00003d15  __aeabi_fcmpge                       
00003d29  __aeabi_fcmpgt                       
00003d3d  I2C_OLED_i2c_sda_unlock              
00003d9d  SYSCFG_DL_I2C_OLED_init              
00003dfd  DL_I2C_fillControllerTXFIFO          
00003e5d  SYSCFG_DL_SYSCTL_init                
00003eb9  Task_Tracker                         
00003f15  frexp                                
00003f15  frexpl                               
00003fcd  Serial_Init                          
00004025  __TI_ltoa                            
000040d5  __aeabi_idiv                         
000040d5  __aeabi_idivmod                      
00004181  DL_UART_drainRXFIFO                  
000041d5  SYSCFG_DL_init                       
0000431d  DL_DMA_initChannel                   
000043b5  OLED_Printf                          
00004401  __aeabi_d2iz                         
00004401  __fixdfsi                            
0000444d  DL_UART_init                         
00004495  Motor_GetSpeed                       
00004525  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000045ad  __aeabi_d2uiz                        
000045ad  __fixunsdfsi                         
000045f1  SYSCFG_DL_UART_bujingA_init          
00004631  SYSCFG_DL_UART_bujingB_init          
00004671  __aeabi_uidiv                        
00004671  __aeabi_uidivmod                     
000046b1  __aeabi_f2d                          
000046b1  __extendsfdf2                        
000046f1  atoi                                 
00004825  I2C_OLED_Set_Pos                     
00004861  __aeabi_i2f                          
00004861  __floatsisf                          
0000489d  __gesf2                              
0000489d  __gtsf2                              
000048d9  __TI_auto_init_nobinit_nopinit       
00004915  __cmpsf2                             
00004915  __eqsf2                              
00004915  __lesf2                              
00004915  __ltsf2                              
00004915  __nesf2                              
00004951  __muldsi3                            
0000498d  Interrupt_Init                       
000049c5  __aeabi_f2iz                         
000049c5  __fixsfsi                            
00004a65  Task_IdleFunction                    
00004b29  SYSCFG_DL_DMA_CH_RX_init             
00004b59  _IQ24toF                             
00004bb9  _IQ24mpy                             
00004c3d  __aeabi_i2d                          
00004c3d  __floatsidf                          
00004c69  vsprintf                             
00004c95  PID_Init                             
00004e79  SysTick_Increasment                  
00004ea1  _c_int00_noargs                      
00004fad  DL_I2C_setClockConfig                
0000501d  __aeabi_ui2d                         
0000501d  __floatunsidf                        
00005041  __aeabi_lmul                         
00005041  __muldi3                             
00005065  PID_SetParams                        
00005087  memccpy                              
000050e9  Delay                                
00005109  main                                 
00005149  __aeabi_llsl                         
00005149  __ashldi3                            
000052d5  DL_Timer_setCaptCompUpdateMethod     
000052f1  DL_Timer_setClockConfig              
00005329  WIT_SetTargetAngle                   
000055fd  DL_Timer_setCaptureCompareOutCtl     
000056d5  SYSCFG_DL_DMA_CH_TX_init             
000056ed  SYSCFG_DL_DMA_WIT_init               
00005855  __aeabi_uldivmod                     
0000587d  DL_UART_setClockConfig               
0000588f  TI_memcpy_small                      
000058a1  __TI_decompress_none                 
000058c5  DL_Timer_setCaptureCompareValue      
000058d5  SYSCFG_DL_DMA_init                   
000058e5  SYSCFG_DL_SYSTICK_init               
000058f5  WIT_IsTargetControlActive            
00005905  wcslen                               
00005915  __TI_zero_init                       
00005925  __aeabi_memset                       
00005925  __aeabi_memset4                      
00005925  __aeabi_memset8                      
0000594f  TI_memset_small                      
0000595d  Sys_GetTick                          
00005969  WIT_CancelTargetControl              
00005975  __aeabi_memclr                       
00005975  __aeabi_memclr4                      
00005975  __aeabi_memclr8                      
00005981  DL_Common_delayCycles                
000059b9  __aeabi_errno_addr                   
000059c1  __aeabi_memcpy                       
000059c1  __aeabi_memcpy4                      
000059c1  __aeabi_memcpy8                      
000059c9  abort                                
000059cf  ADC0_IRQHandler                      
000059cf  ADC1_IRQHandler                      
000059cf  AES_IRQHandler                       
000059cf  CANFD0_IRQHandler                    
000059cf  DAC0_IRQHandler                      
000059cf  DMA_IRQHandler                       
000059cf  Default_Handler                      
000059cf  GROUP0_IRQHandler                    
000059cf  HardFault_Handler                    
000059cf  I2C0_IRQHandler                      
000059cf  I2C1_IRQHandler                      
000059cf  NMI_Handler                          
000059cf  PendSV_Handler                       
000059cf  RTC_IRQHandler                       
000059cf  SPI0_IRQHandler                      
000059cf  SPI1_IRQHandler                      
000059cf  SVC_Handler                          
000059cf  TIMA0_IRQHandler                     
000059cf  TIMA1_IRQHandler                     
000059cf  TIMG0_IRQHandler                     
000059cf  TIMG12_IRQHandler                    
000059cf  TIMG6_IRQHandler                     
000059cf  TIMG7_IRQHandler                     
000059cf  TIMG8_IRQHandler                     
000059cf  UART1_IRQHandler                     
000059cf  UART2_IRQHandler                     
000059cf  UART3_IRQHandler                     
000059d2  C$$EXIT                              
000059d3  HOSTexit                             
000059d7  Reset_Handler                        
000059ed  _system_pre_init                     
000059f0  asc2_1608                            
00005fe0  asc2_0806                            
00006210  __aeabi_ctype_table_                 
00006210  __aeabi_ctype_table_C                
00006311  _IQ6div_lookup                       
000064cc  __TI_Handler_Table_Base              
000064d8  __TI_Handler_Table_Limit             
000064e0  __TI_CINIT_Base                      
000064f0  __TI_CINIT_Limit                     
000064f0  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorBFrontBackup                   
202003ac  gMotorAFrontBackup                   
2020044c  gUART_bujingBBackup                  
202004a8  wit_dmaBuffer                        
202004ca  ret                                  
202004cc  wit_data                             
202004ec  ExISR_Flag                           
202004f0  _sys_memory                          
202008f0  Motor_Font_Left                      
20200938  Motor_Font_Right                     
20200980  Data_Tracker_Input                   
20200988  Motor                                
20200990  Data_MotorEncoder                    
20200994  Data_Motor_TarSpeed                  
20200998  Data_Tracker_Offset                  
2020099c  Data_wit_Offset                      
202009a0  Data_wit_Target                      
202009a4  Data_wit_UserTarget                  
202009ac  __aeabi_errno                        
202009b0  delayTick                            
202009b8  uwTick                               
202009be  Data_wit_ControlEnabled              
202009c1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[265 symbols]
