#include "Motor.h"

// DRV8871双PWM控制模式 - 移除GPIO方向控制宏定义

/**
 * @brief 统一的PWM设置函数 - 处理TimerG和TimerA的API差异
 *
 * @param Motor 电机实例指针
 * @param channel PWM通道 (DL_TIMER_CC_0_INDEX 或 DL_TIMER_CC_1_INDEX)
 * @param value PWM占空比值
 */
static void SetPWMValue(MOTOR_Def_t *Motor, int channel, uint32_t value)
{
    if (Motor == NULL) return;

    if (Motor->Is_TimerG) {
        // 使用TimerG API (MotorAFront)
        DL_TimerG_setCaptureCompareValue(Motor->Motor_PWM_TIMX, value, channel);
    } else {
        // 使用TimerA API (MotorBFront)
        DL_TimerA_setCaptureCompareValue(Motor->Motor_PWM_TIMX, value, channel);
    }
}

/**
 * @brief 计算PWM占空比值 - 处理不同定时器的周期差异
 *
 * @param Motor 电机实例指针
 * @param percentage 占空比百分比 (0-100)
 * @return uint32_t 实际的PWM占空比值
 */
static uint32_t CalculateDutyValue(MOTOR_Def_t *Motor, float percentage)
{
    if (Motor == NULL) return 0;

    // 根据定时器周期计算实际占空比值
    // MotorAFront: period=100, MotorBFront: period=1000
    return (uint32_t)(fabs(percentage) * Motor->Motor_PWM_Period / 100.0f);
}

//左前轮 - DRV8871双PWM控制 (MotorAFront/TimerG)
MOTOR_Def_t Motor_Font_Left = {.Motor_Dirc = DIRC_FOWARD,
                               .Motor_Encoder_Addr = &Data_MotorEncoder[0],
                               .Motor_PWM_TIMX = MotorAFront_INST,
                               .Motor_PWM_CH1 = DL_TIMER_CC_0_INDEX, //IN1通道
                               .Motor_PWM_CH2 = DL_TIMER_CC_1_INDEX, //IN2通道
                               .Motor_PWM_Period = 100, //TimerG周期值
                               .Is_TimerG = true}; //TimerG类型
//右前轮 - DRV8871双PWM控制 (MotorBFront/TimerA)
MOTOR_Def_t Motor_Font_Right = {.Motor_Dirc = DIRC_FOWARD,
                                .Motor_Encoder_Addr = &Data_MotorEncoder[1],
                                .Motor_PWM_TIMX = MotorBFront_INST,
                                .Motor_PWM_CH1 = DL_TIMER_CC_0_INDEX, //IN1通道
                                .Motor_PWM_CH2 = DL_TIMER_CC_1_INDEX, //IN2通道
                                .Motor_PWM_Period = 1000, //TimerA周期值
                                .Is_TimerG = false}; //TimerA类型
/**
 * @brief 开启电机 - DRV8871双PWM控制模式
 *
 * 初始化流程：
 * 1. 启动双PWM定时器 (MotorAFront/TimerG + MotorBFront/TimerA)
 * 2. 设置双PWM通道为滑行状态 (IN1=0, IN2=0)
 * 3. 初始化PID控制系统
 */
void Motor_Start(void)
{
    // 启动双PWM定时器
    DL_TimerG_startCounter(MotorAFront_INST); //开启左前轮(TimerG)
    DL_TimerA_startCounter(MotorBFront_INST); //开启右前轮(TimerA)

    // 设置双PWM通道为安全的滑行状态 (IN1=0, IN2=0)
    Motor_SetDuty(&Motor_Font_Left, 0.0f);
    Motor_SetDuty(&Motor_Font_Right, 0.0f);

    // 初始化PID控制系统
    PID_Init(&Motor_Font_Left.Motor_PID_Instance);
    PID_Init(&Motor_Font_Right.Motor_PID_Instance);

    // 设置PID控制参数
    PID_SetParams(&Motor_Font_Left.Motor_PID_Instance, 2.0f, 1.0f, 0.15f);
    PID_SetParams(&Motor_Font_Right.Motor_PID_Instance, 2.0f, 1.0f, 0.15f);
}

// Motor_SetDirc函数已移除 - DRV8871双PWM控制模式不需要GPIO方向控制

/**
 * @brief 设置对应的电机占空比值 - DRV8871双PWM控制模式
 *
 * @param Motor  电机实例指针
 * @param value PWM占空比 (-100 ~ +100, 负值反转，正值正转)
 * @return 返回设置成功与否
 *
 * DRV8871控制逻辑：
 * - 正转(01): IN1=PWM, IN2=0 (电流OUT1→OUT2)
 * - 反转(10): IN1=0, IN2=PWM (电流OUT2→OUT1)
 * - 滑行(00): IN1=0, IN2=0 (高阻态，自由滑行)
 * - 刹车(11): IN1=PWM, IN2=PWM (低侧慢衰减，暂未实现)
 */
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value)
{
    if (Motor == NULL) return false;

    // 限制范围
    if (value > 100.0f) value = 100.0f;
    if (value < -100.0f) value = -100.0f;

    // 计算占空比值（考虑不同定时器的周期差异）
    uint32_t duty = CalculateDutyValue(Motor, value);

    if (value > 0) {
        // 正转: IN1=PWM, IN2=0
        Motor->Motor_Dirc = DIRC_FOWARD;
        SetPWMValue(Motor, Motor->Motor_PWM_CH1, duty);
        SetPWMValue(Motor, Motor->Motor_PWM_CH2, 0);
    } else if (value < 0) {
        // 反转: IN1=0, IN2=PWM
        Motor->Motor_Dirc = DIRC_BACKWARD;
        SetPWMValue(Motor, Motor->Motor_PWM_CH1, 0);
        SetPWMValue(Motor, Motor->Motor_PWM_CH2, duty);
    } else {
        // 停止: IN1=0, IN2=0 (滑行)
        Motor->Motor_Dirc = DIRC_NONE;
        SetPWMValue(Motor, Motor->Motor_PWM_CH1, 0);
        SetPWMValue(Motor, Motor->Motor_PWM_CH2, 0);
    }

    return true;
}

/**
 * @brief 获取电机速度 更新到PID实例中
 * 
 * @param Motor 电机
 * @param time 读取时间间隔 ms
 * @return true 
 * @return false 
 */
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time)
{
    if (Motor == NULL) return false;

    Motor->Motor_PID_Instance.Acutal_Now =*Motor->Motor_Encoder_Addr;
    *Motor->Motor_Encoder_Addr = 0;

    return true;
}





