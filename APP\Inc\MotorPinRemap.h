#ifndef __MotorPinRemap_h
#define __MotorPinRemap_h

/**
 * @file MotorPinRemap.h
 * @brief 电机引脚重映射配置文件
 * @version 1.0
 * @date 2025-07-30
 * <AUTHOR> (Engineer)
 * 
 * @description
 * 本文件解决TI_CAR1.6工程中编码器和PWM引脚左右映射颠倒的问题。
 * 通过应用层重映射机制，将用户要求的逻辑电机配置映射到当前的物理引脚配置。
 * 
 * 用户要求的引脚配置：
 * - 左电机：编码器A(PB9)、编码器B(PB8)、PWM(PB2、PB3)
 * - 右电机：编码器A(PB11)、编码器B(PB10)、PWM(PB16、PB15)
 * 
 * 当前物理引脚配置：
 * - LEFT标识：编码器A(PB11)、编码器B(PB10)、PWM(PB15、PB16) - MotorAFront/TIMG7
 * - RIGHT标识：编码器A(PB9)、编码器B(PB8)、PWM(PB2、PB3) - MotorBFront/TIMA1
 * 
 * 重映射策略：
 * - 逻辑左电机 -> 物理RIGHT配置 (用户要求的左电机引脚对应当前RIGHT配置)
 * - 逻辑右电机 -> 物理LEFT配置 (用户要求的右电机引脚对应当前LEFT配置)
 */

#include "ti_msp_dl_config.h"

// =============================================================================
// 编码器引脚重映射定义
// =============================================================================

/**
 * @brief 逻辑左电机编码器引脚映射
 * 用户要求：左电机编码器A(PB9)、编码器B(PB8)
 * 映射到：当前RIGHT配置的物理引脚
 */
#define LOGICAL_LEFT_ENCODER_A_PIN    SPD_READER_A_FONT_RIGHT_A_PIN  // PB9
#define LOGICAL_LEFT_ENCODER_B_PIN    SPD_READER_B_FONT_RIGHT_B_PIN  // PB8

/**
 * @brief 逻辑右电机编码器引脚映射  
 * 用户要求：右电机编码器A(PB11)、编码器B(PB10)
 * 映射到：当前LEFT配置的物理引脚
 */
#define LOGICAL_RIGHT_ENCODER_A_PIN   SPD_READER_A_FONT_LEFT_A_PIN   // PB11
#define LOGICAL_RIGHT_ENCODER_B_PIN   SPD_READER_B_FONT_LEFT_B_PIN   // PB10

// =============================================================================
// PWM定时器重映射定义
// =============================================================================

/**
 * @brief 逻辑左电机PWM定时器映射
 * 用户要求：左电机PWM(PB2、PB3)
 * 映射到：MotorBFront_INST (TIMA1) - 当前RIGHT配置
 */
#define LOGICAL_LEFT_PWM_TIMER        MotorBFront_INST               // TIMA1
#define LOGICAL_LEFT_PWM_CH1          DL_TIMER_CC_0_INDEX            // IN1通道
#define LOGICAL_LEFT_PWM_CH2          DL_TIMER_CC_1_INDEX            // IN2通道
#define LOGICAL_LEFT_PWM_PERIOD       1000                           // TimerA周期值
#define LOGICAL_LEFT_IS_TIMERG        false                          // TimerA类型

/**
 * @brief 逻辑右电机PWM定时器映射
 * 用户要求：右电机PWM(PB16、PB15)  
 * 映射到：MotorAFront_INST (TIMG7) - 当前LEFT配置
 */
#define LOGICAL_RIGHT_PWM_TIMER       MotorAFront_INST               // TIMG7
#define LOGICAL_RIGHT_PWM_CH1         DL_TIMER_CC_0_INDEX            // IN1通道
#define LOGICAL_RIGHT_PWM_CH2         DL_TIMER_CC_1_INDEX            // IN2通道
#define LOGICAL_RIGHT_PWM_PERIOD      100                            // TimerG周期值
#define LOGICAL_RIGHT_IS_TIMERG       true                           // TimerG类型

// =============================================================================
// 编码器数据数组索引重映射
// =============================================================================

/**
 * @brief 编码器数据数组索引映射
 * 确保逻辑左右电机对应正确的编码器数据索引
 */
#define LOGICAL_LEFT_ENCODER_INDEX    1    // 逻辑左电机使用Data_MotorEncoder[1]
#define LOGICAL_RIGHT_ENCODER_INDEX   0    // 逻辑右电机使用Data_MotorEncoder[0]

// =============================================================================
// 引脚配置验证宏
// =============================================================================

/**
 * @brief 编译时引脚配置验证
 * 确保重映射配置与用户要求一致
 */
#if (LOGICAL_LEFT_ENCODER_A_PIN != DL_GPIO_PIN_9)
#error "逻辑左电机编码器A引脚配置错误：应为PB9"
#endif

#if (LOGICAL_LEFT_ENCODER_B_PIN != DL_GPIO_PIN_8)
#error "逻辑左电机编码器B引脚配置错误：应为PB8"
#endif

#if (LOGICAL_RIGHT_ENCODER_A_PIN != DL_GPIO_PIN_11)
#error "逻辑右电机编码器A引脚配置错误：应为PB11"
#endif

#if (LOGICAL_RIGHT_ENCODER_B_PIN != DL_GPIO_PIN_10)
#error "逻辑右电机编码器B引脚配置错误：应为PB10"
#endif

// =============================================================================
// 调试信息宏定义
// =============================================================================

#ifdef DEBUG
/**
 * @brief 引脚重映射调试信息
 * 在调试模式下提供引脚配置信息
 */
#define MOTOR_REMAP_DEBUG_INFO() \
    do { \
        /* 逻辑左电机配置信息 */ \
        /* 编码器：PB9(A), PB8(B) -> MotorBFront/TIMA1 */ \
        /* 逻辑右电机配置信息 */ \
        /* 编码器：PB11(A), PB10(B) -> MotorAFront/TIMG7 */ \
    } while(0)
#else
#define MOTOR_REMAP_DEBUG_INFO()
#endif

#endif // __MotorPinRemap_h
