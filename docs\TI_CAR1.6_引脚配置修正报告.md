# TI_CAR1.6 引脚配置修正报告

## 文档信息
- **项目名称**: TI_CAR1.6 嵌入式系统
- **修正日期**: 2025-07-30
- **修正版本**: v1.0
- **负责工程师**: <PERSON> (Engineer)
- **审核状态**: 已完成

## 1. 修改概述

### 1.1 问题描述
TI_CAR1.6工程中存在电机编码器和PWM引脚左右映射颠倒的问题，当前代码配置与用户硬件连接要求不一致：

**用户硬件要求**：
- 左电机：编码器A(PB9)、编码器B(PB8)、PWM(PB2、PB3)
- 右电机：编码器A(PB11)、编码器B(PB10)、PWM(PB16、PB15)

**当前代码配置**：
- 左电机标识：编码器A(PB11)、编码器B(PB10)、PWM(PB15、PB16) - MotorAFront/TIMG7
- 右电机标识：编码器A(PB9)、编码器B(PB8)、PWM(PB2、PB3) - MotorBFront/TIMA1

### 1.2 解决方案
采用应用层重映射机制，通过宏定义将逻辑电机配置映射到正确的物理引脚，在不修改底层框架的前提下解决引脚配置问题。

### 1.3 修改目标
- ✅ 解决编码器和PWM引脚左右颠倒问题
- ✅ 保持底层框架(ti_msp_dl_config.h)不变
- ✅ 确保与用户硬件连接要求一致
- ✅ 维护系统架构稳定性
- ✅ 保持现有功能完整性

## 2. 引脚对应关系

### 2.1 修改前后对比表

| 逻辑电机 | 用户要求引脚 | 修改前代码标识 | 修改后重映射 | 物理定时器 |
|---------|-------------|---------------|-------------|-----------|
| 左电机编码器A | PB9 | SPD_READER_A_FONT_LEFT_A_PIN (PB11) | LOGICAL_LEFT_ENCODER_A_PIN → SPD_READER_A_FONT_RIGHT_A_PIN (PB9) | - |
| 左电机编码器B | PB8 | SPD_READER_B_FONT_LEFT_B_PIN (PB10) | LOGICAL_LEFT_ENCODER_B_PIN → SPD_READER_B_FONT_RIGHT_B_PIN (PB8) | - |
| 左电机PWM | PB2、PB3 | MotorAFront_INST (TIMG7) | LOGICAL_LEFT_PWM_TIMER → MotorBFront_INST (TIMA1) | TIMA1 |
| 右电机编码器A | PB11 | SPD_READER_A_FONT_RIGHT_A_PIN (PB9) | LOGICAL_RIGHT_ENCODER_A_PIN → SPD_READER_A_FONT_LEFT_A_PIN (PB11) | - |
| 右电机编码器B | PB10 | SPD_READER_B_FONT_RIGHT_B_PIN (PB8) | LOGICAL_RIGHT_ENCODER_B_PIN → SPD_READER_B_FONT_LEFT_B_PIN (PB10) | - |
| 右电机PWM | PB16、PB15 | MotorBFront_INST (TIMA1) | LOGICAL_RIGHT_PWM_TIMER → MotorAFront_INST (TIMG7) | TIMG7 |

### 2.2 重映射策略
- **逻辑左电机** → **物理RIGHT配置** (用户要求的左电机引脚对应当前RIGHT配置)
- **逻辑右电机** → **物理LEFT配置** (用户要求的右电机引脚对应当前LEFT配置)

## 3. 详细修改内容

### 3.1 新增文件

#### APP/Inc/MotorPinRemap.h
- **作用**: 应用层引脚重映射配置文件
- **内容**: 
  - 编码器引脚重映射定义
  - PWM定时器重映射定义
  - 编码器数据数组索引重映射
  - 编译时配置验证宏
  - 调试信息宏定义

### 3.2 修改文件

#### APP/Inc/Interrupt.h
- **修改内容**: 包含MotorPinRemap.h头文件
- **影响**: 中断处理模块可访问重映射定义

#### APP/Src/Interrupt.c
- **修改内容**: 
  - 中断处理逻辑使用重映射引脚定义
  - GROUP1_IRQHandler函数更新
  - Interrupt_Init函数更新
- **影响**: 编码器中断处理使用正确的逻辑引脚

#### BSP/Src/Motor.c
- **修改内容**:
  - 包含MotorPinRemap.h头文件
  - Motor_Font_Left和Motor_Font_Right实例配置更新
  - Motor_Start函数定时器启动逻辑更新
- **影响**: 电机实例使用重映射的定时器和编码器配置

#### APP/Inc/SysConfig.h
- **修改内容**: 添加MotorPinRemap.h包含
- **影响**: 重映射配置在全系统生效

## 4. 重映射机制工作原理

### 4.1 宏定义重映射
通过预处理器宏定义，将逻辑引脚名称重定向到正确的物理引脚：

```c
// 逻辑左电机编码器映射到物理RIGHT配置
#define LOGICAL_LEFT_ENCODER_A_PIN    SPD_READER_A_FONT_RIGHT_A_PIN  // PB9
#define LOGICAL_LEFT_ENCODER_B_PIN    SPD_READER_B_FONT_RIGHT_B_PIN  // PB8

// 逻辑右电机编码器映射到物理LEFT配置  
#define LOGICAL_RIGHT_ENCODER_A_PIN   SPD_READER_A_FONT_LEFT_A_PIN   // PB11
#define LOGICAL_RIGHT_ENCODER_B_PIN   SPD_READER_B_FONT_LEFT_B_PIN   // PB10
```

### 4.2 定时器重映射
PWM定时器实例重映射，确保逻辑电机使用正确的物理定时器：

```c
// 逻辑左电机使用MotorBFront_INST (TIMA1)
#define LOGICAL_LEFT_PWM_TIMER        MotorBFront_INST

// 逻辑右电机使用MotorAFront_INST (TIMG7)  
#define LOGICAL_RIGHT_PWM_TIMER       MotorAFront_INST
```

### 4.3 编译时验证
通过编译时检查确保重映射配置正确：

```c
#if (LOGICAL_LEFT_ENCODER_A_PIN != DL_GPIO_PIN_9)
#error "逻辑左电机编码器A引脚配置错误：应为PB9"
#endif
```

## 5. 验证测试结果

### 5.1 编译验证
- ✅ 所有文件编译无错误和警告
- ✅ 重映射宏定义正确展开
- ✅ 编译时验证宏通过检查

### 5.2 功能验证
- ✅ 中断处理逻辑使用正确的逻辑引脚
- ✅ 电机实例配置使用重映射的定时器
- ✅ 系统配置文件正确包含重映射头文件
- ✅ 重映射配置在全系统生效

### 5.3 引脚配置验证
- ✅ 逻辑左电机对应用户要求的PB9/PB8编码器和PB2/PB3 PWM
- ✅ 逻辑右电机对应用户要求的PB11/PB10编码器和PB16/PB15 PWM
- ✅ 编码器数据数组索引正确映射
- ✅ PWM定时器类型标志正确设置

## 6. 使用注意事项

### 6.1 开发注意事项
1. **头文件包含顺序**: 确保MotorPinRemap.h在ti_msp_dl_config.h之后包含
2. **宏定义使用**: 在代码中使用LOGICAL_*宏定义，避免直接使用物理引脚名称
3. **编译验证**: 利用编译时验证宏确保配置正确性
4. **调试模式**: 在DEBUG模式下可使用MOTOR_REMAP_DEBUG_INFO()获取配置信息

### 6.2 维护注意事项
1. **底层配置**: 不要修改ti_msp_dl_config.h文件，保持底层框架稳定
2. **重映射配置**: 如需调整引脚配置，仅修改MotorPinRemap.h文件
3. **版本控制**: 重要配置变更需要更新文档版本号
4. **测试验证**: 任何配置修改后都需要进行完整的功能测试

### 6.3 故障排查
1. **编译错误**: 检查头文件包含顺序和宏定义语法
2. **功能异常**: 验证重映射配置是否与硬件连接一致
3. **性能问题**: 确认定时器类型标志与实际使用的定时器匹配
4. **调试信息**: 使用调试宏获取当前配置状态

## 7. 总结

本次引脚配置修正通过应用层重映射机制成功解决了TI_CAR1.6工程中电机引脚左右颠倒的问题。修正方案具有以下优点：

1. **非侵入性**: 不修改底层框架，保持系统稳定性
2. **可维护性**: 配置集中管理，便于后续调整
3. **可验证性**: 编译时验证确保配置正确性
4. **可追溯性**: 详细文档记录所有修改内容

修正后的系统完全符合用户硬件连接要求，为后续开发和维护奠定了坚实基础。

---
**文档结束**
